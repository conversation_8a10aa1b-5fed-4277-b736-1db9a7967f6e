//@ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Scrollbars} from 'react-custom-scrollbars-2';
import { TopBarContext } from '../Context/TopBarContext';
import { AlertContext } from '../NotificationAlertService/AlertList';
import { Plus, Upload } from 'lucide-react';

import ProjectItem from './ProjectItem';
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { EditTitleModal, DeleteProjectModal, CloneProjectModal } from './ProjectActionModals';
import { updateProjectTitle, deleteProject, cloneProject } from '../../utils/projectApi'; // You'll need to implement these API functions
import ImportProjectModal from './ImportProjectModal';

export interface ProjectListProps {
  projects: any[];
  onProjectClick?: (project: any) => void;
  selectedProjectId?: string;
  closeSidebar?: () => void;
  createNewProjectAndCloseSidebar?: () => void;
  openProjectModal?: () => void;
  isSharedTab?: boolean;
  refreshProjectsList?: () => void;
  theme?: 'light' | 'dark';
}

const ProjectList: React.FC<ProjectListProps> = ({
  projects,
  onProjectClick,
  selectedProjectId: propSelectedProjectId,
  closeSidebar,
  openProjectModal,
  isSharedTab = false,
  refreshProjectsList,
  theme = 'light'
}) => {


  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState(projects);
  const [selectedProjectId, setSelectedProjectId] = useState<string>(() => {
    return sessionStorage.getItem('selectedProjectId') || '';
  });


  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [cloneModalOpen, setCloneModalOpen] = useState(false);
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [isCloning, setIsCloning] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedProject, setSelectedProject] = useState<any>(null);

  const { getActiveTab } = useContext(TopBarContext);
  const { showAlert } = useContext(AlertContext);
  const [isInitialDataEmpty, setIsInitialDataEmpty] = useState(projects.length === 0);

  // Theme classes
  const themeClasses = {
    light: {
      container: "chat-list flex flex-col h-full w-full bg-white",
      searchContainer: "p-4 border-b border-gray-200 bg-white",
      searchInput: "w-full h-[38px] pl-12 pr-4 rounded-lg border border-gray-200 bg-white text-gray-900 placeholder-gray-400 text-base focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",
      searchIcon: "text-gray-400",
      listContainer: "chat-list-container pb-16 bg-white min-h-full",
      createButtonContainer: "px-3 py-4 bg-white border-t border-gray-200 create-project-button-container",
      createButton: "w-full py-2 bg-orange-500 hover:bg-orange-600 text-white text-base font-medium rounded",
      scrollThumb: "rgba(0, 0, 0, 0.2)",
      scrollTrack: "transparent"
    },
    dark: {
      container: "chat-list flex flex-col h-full w-full bg-[#231f20]",
      searchContainer: "p-4 border-b border-gray-700 bg-[#231f20]",
      searchInput: "w-full h-[38px] pl-12 pr-4 rounded-lg border border-gray-700 bg-gray-800 text-gray-100 placeholder-gray-400 text-base focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",
      searchIcon: "text-gray-400",
      listContainer: "chat-list-container pb-16 bg-[#231f20] min-h-full",
      createButtonContainer: "px-3 py-4 bg-[#231f20] border-t border-gray-700 create-project-button-container",
      createButton: "w-full py-2 bg-orange-600 hover:bg-orange-700 text-white text-base font-medium rounded",
      scrollThumb: "rgba(255, 255, 255, 0.2)",
      scrollTrack: "transparent"
    }
  };

  useEffect(() => {
    const results = projects.filter((project) =>
      project.Title?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setSearchResults(results);
  }, [searchTerm, projects]);

  const handleProjectClick = (project: any) => {
    onProjectClick?.(project);
    setSelectedProjectId(project.id);
    sessionStorage.setItem('selectedProjectId', project.id);
    closeSidebar?.();
  };

  const handleClearSearch = () => {
    setSearchTerm("");
  }
  
  const createNewProject = () => {
    openProjectModal?.();
    closeSidebar?.();
  };

  const handleImportProject = () => {
    setImportModalOpen(true);
  };

  // Project action handlers
  const handleEditTitle = (project: any) => {
    setSelectedProject(project);
    setEditModalOpen(true);
  };

  const handleDeleteProject = (project: any) => {
    setSelectedProject(project);
    setDeleteModalOpen(true);
  };

  const handleCloneProject = (project: any) => {
    setSelectedProject(project);
    setCloneModalOpen(true);
  };

  // API action handlers
  const handleSaveTitle = async (projectId: string, newTitle: string) => {
    setIsEditing(true);
    try {
      // Call the API to update the project title
      const response = await updateProjectTitle(projectId, newTitle);
      
      // Show success toast notification only if backend operation succeeded
      if (response) {
        // Close the modal
        setEditModalOpen(false);
        
        showAlert(`Project title updated to "${newTitle}"`, 'success');
        
        // Refresh the project list
        if (refreshProjectsList) {
          refreshProjectsList();
        }
      }
    } catch (error) {
      console.error('Error updating project title:', error);
      showAlert('Failed to update project title', 'error');
    } finally {
      setIsEditing(false);
    }
  };

  const handleConfirmDelete = async (projectId: string) => {
    setIsDeleting(true);
    try {
      // Call the API to delete the project
      const response = await deleteProject(projectId);
      
      // Show success toast notification only if backend operation succeeded
      if (response) {
        // Close the modal
        setDeleteModalOpen(false);
        
        showAlert('Project deleted successfully', 'success');
        
        // Refresh the project list
        if (refreshProjectsList) {
          refreshProjectsList();
        }
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      showAlert('Failed to delete project', 'error');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleConfirmClone = async (projectId: string, newTitle: string) => {
    
    setIsCloning(true);
    try {
      // Call the API to clone the project
      const clonedProject = await cloneProject(projectId, newTitle);
      
      // Show success toast notification only if backend operation succeeded
      if (clonedProject) {
        // Close the modal
        setCloneModalOpen(false);
        
        showAlert(`Project "${newTitle}" cloned successfully`, 'success');
        
        // Refresh the project list
        if (refreshProjectsList) {
          refreshProjectsList();
        }
      }
    } catch (error) {
      console.error('Error cloning project:', error);
      showAlert('Failed to clone project', 'error');
    } finally {
      setIsCloning(false);
    }
  };


  useEffect(() => {
    const storedProjectId = sessionStorage.getItem('selectedProjectId');
    if (storedProjectId) setSelectedProjectId(storedProjectId);
  }, []);

  useEffect(() => {
    setIsInitialDataEmpty(projects.length === 0);
  }, [projects]);

  return (
    <div className={themeClasses[theme].container}>
   <div className={themeClasses[theme].searchContainer}>
        <div className="relative">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search"
            className={themeClasses[theme].searchInput}
          />
          <div className="absolute left-4 top-1/2 -translate-y-1/2">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={themeClasses[theme].searchIcon} >
              <circle cx="7" cy="7" r="6" stroke="currentColor" strokeWidth="1.5"/>
              <path d="M11 11L14.5 14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
            </svg>
          </div>
        </div>
      </div>
      <Scrollbars
        style={{ 
          flex: 1,
          backgroundColor: theme === 'light' ? '#ffffff' : '#231f20'
        }}
        autoHide
        autoHideTimeout={1000}
        autoHideDuration={200}
        hideTracksWhenNotNeeded
        renderThumbVertical={({ style, ...props }) => (
          <div
            {...props}
            style={{
              ...style,
              backgroundColor: themeClasses[theme].scrollThumb,
              borderRadius: '4px',
              width: '6px',
            }}
          />
        )}
        renderTrackVertical={({ style, ...props }) => (
          <div
            {...props}
            style={{
              ...style,
              position: 'absolute',
              width: '6px',
              right: '2px',
              bottom: '2px',
              top: '2px',
              borderRadius: '3px',
            }}
          />
        )}
        renderView={({ style, ...props }) => (
          <div
            {...props}
            style={{
              ...style,
              backgroundColor: theme === 'light' ? '#ffffff' : '#231f20',
              minHeight: '100%'
            }}
          />
        )}
      >
        <div id='sidebar-project-list' className={themeClasses[theme].listContainer} >
          {isInitialDataEmpty ? (
            <EmptyStateView type="projects" onClick={() => createNewProject()} theme={theme} />
          ) :
            searchResults.length > 0 ? (
              searchResults.map((project, index) => (
                <ProjectItem
                  key={index}
                  project={project}
                  isSelected={selectedProjectId === project.id}
                  onClick={() => handleProjectClick(project)}
                  onEditTitle={handleEditTitle}
                  onDeleteProject={handleDeleteProject}
                  onCloneProject={handleCloneProject}
                  isSharedProject={isSharedTab || project.isPublic} // Check both the tab and the project's own public status
                  theme={theme}
                />
              ))
            ) : (
              <EmptyStateView type="noSearchResult" onClick={handleClearSearch} theme={theme}/>
            )}
        </div>
      </Scrollbars>
      
      {/* Create New Project and import button at the bottom */}
      {!isSharedTab && (
        <div className="px-3 py-4 bg-white border-t border-gray-200 create-project-button-container flex gap-2">
          <button 
            onClick={createNewProject}
            className="flex-1 py-2 bg-orange-500 hover:bg-orange-600 text-white text-base font-medium rounded flex items-center justify-center"
          >
            <Plus size={16} className="mr-1" />
            New
          </button>
          <button 
            className="flex-1 py-2 border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 text-base font-medium rounded flex items-center justify-center"
            onClick={handleImportProject}
          >
            <Upload size={16} className="mr-1" />
            Import
          </button>
        </div>
      )}

      {/* Modals */}
      {selectedProject && (
        <>
          <EditTitleModal
            isOpen={editModalOpen}
            onClose={() => setEditModalOpen(false)}
            project={selectedProject}
            onSave={handleSaveTitle}
            isLoading={isEditing}
          />
          
          <DeleteProjectModal
            isOpen={deleteModalOpen}
            onClose={() => setDeleteModalOpen(false)}
            project={selectedProject}
            onConfirm={handleConfirmDelete}
            isLoading={isDeleting}
          />
          
          <CloneProjectModal
            isOpen={cloneModalOpen}
            onClose={() => setCloneModalOpen(false)}
            project={selectedProject}
            onConfirm={handleConfirmClone}
            isLoading={isCloning}
          />
        </>
      )}

      <ImportProjectModal
        isOpen={importModalOpen}
        onClose={() => setImportModalOpen(false)}
        refreshProjectsList={refreshProjectsList}
      />

    </div>
  );
};

ProjectList.displayName = 'ProjectList';

export default ProjectList;