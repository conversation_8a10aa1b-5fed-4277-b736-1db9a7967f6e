/**
 * Utility function to handle tech stack updates without UI flickering
 * 
 * This function creates a complete tech stack update that maintains exclusivity
 * between frontend and backend frameworks (when one is selected, the other is set to "None")
 * 
 * @param {Object} currentTechStack The current tech stack state
 * @param {string} category The category being updated ('frontend', 'backend', 'language')
 * @param {string} value The new value for the category
 * @returns {Object} A complete updated tech stack object
 */

export function createTechStackUpdate(
  currentTechStack,
  category,
  value
) {
  // Create a complete copy of the current tech stack
  const newTechStack = {
    ...currentTechStack
  };
  
  // Type-safe property assignment without mutual exclusivity
  if (category === 'frontend') {
    newTechStack.frontend = [value];
    // No longer forces backend to "None"
  } 
  else if (category === 'backend') {
    newTechStack.backend = [value];
    // No longer forces frontend to "None"
  }
  else if (category === 'language') {
    newTechStack.language = [value];
  }
  
  return newTechStack;
}

/**
 * Updates the blueprint state with a new tech stack in a single operation
 * to avoid flickering from multiple state updates
 * 
 * @param {Function} setBlueprint React setState function for the blueprint
 * @param {string} category The category being updated ('frontend', 'backend', 'language')
 * @param {string} value The new value for the category
 */
export function updateBlueprintWithTechStack(
  setBlueprint,
  category,
  value
) {
  setBlueprint(prevBlueprint => {
    const newTechStack = createTechStackUpdate(
      prevBlueprint.techStack,
      category,
      value
    );
    
    return {
      ...prevBlueprint,
      techStack: newTechStack
    };
  });
} 